"use client"

import * as React from "react"
import {
  type ColumnDef,
  type ColumnFiltersState,
  type SortingState,
  type RowSelectionState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"
import { ArrowUpDown, Filter, Users, UserCheck, Calendar, Building2, CheckCircle, MoreHorizontal, User } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { StatusBadge } from "@/components/status-badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { useIsMobile } from "@/components/ui/use-mobile"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import type { EmployeeAppraisal, UserRole, AppraisalStatus } from "@/lib/types"
import Link from "next/link"
import { debug } from "@/lib/debug"

interface AppraisalDashboardTableProps {
  data: EmployeeAppraisal[]
  onRefresh?: () => void
  onBulkAction?: (action: string, selectedIds: string[]) => void
}

// Helper function to get role badge color
function getRoleBadgeVariant(role: UserRole): "default" | "secondary" | "destructive" | "outline" {
  switch (role) {
    case 'super-admin':
      return 'destructive'
    case 'hr-admin':
    case 'admin':
      return 'default'
    case 'manager':
      return 'secondary'
    case 'accountant':
      return 'outline'
    default:
      return 'outline'
  }
}

// Helper function to safely format dates for hydration
function formatDateSafe(dateString: string | undefined): string {
  if (!dateString) return '-'

  try {
    const date = new Date(dateString)
    // Use a consistent format that works on both server and client
    return date.toISOString().split('T')[0] // YYYY-MM-DD format
  } catch (error) {
    console.error('Date formatting error:', error)
    return '-'
  }
}

export function AppraisalDashboardTable({
  data,
  onRefresh,
  onBulkAction
}: AppraisalDashboardTableProps) {
  const [sorting, setSorting] = React.useState<SortingState>([])
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const [rowSelection, setRowSelection] = React.useState<RowSelectionState>({})
  const isMobile = useIsMobile()

  // Mobile card component for responsive design
  const MobileAppraisalCard = ({ appraisal }: { appraisal: EmployeeAppraisal }) => (
    <Card key={appraisal.employeeId} className="mb-4">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="space-y-1 flex-1 min-w-0">
            <div className="flex items-center gap-2">
              <Checkbox
                checked={rowSelection[appraisal.employeeId] || false}
                onCheckedChange={(checked) => {
                  setRowSelection(prev => ({
                    ...prev,
                    [appraisal.employeeId]: checked
                  }))
                }}
                aria-label={`Select ${appraisal.fullName}`}
                className="touch-target-sm"
              />
              <h3 className="mobile-heading-2 leading-none truncate">{appraisal.fullName}</h3>
            </div>
            <p className="mobile-caption text-muted-foreground truncate">{appraisal.departmentName}</p>
            {appraisal.managerName && (
              <p className="mobile-caption text-muted-foreground truncate">
                Reports to: {appraisal.managerName}
              </p>
            )}
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="touch-target flex-shrink-0"
                aria-label={`Actions for ${appraisal.fullName}`}
              >
                <span className="sr-only">Open menu for {appraisal.fullName}</span>
                <MoreHorizontal className="h-5 w-5" aria-hidden="true" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuLabel>Actions for {appraisal.fullName}</DropdownMenuLabel>
              <DropdownMenuItem asChild>
                <Link href={`/dashboard/employees/${appraisal.employeeId}/profile`} className="flex items-center touch-target">
                  <User className="mr-2 h-4 w-4" />
                  View Profile
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href={`/dashboard/appraisal/${appraisal.employeeId}`} className="flex items-center touch-target">
                  <MoreHorizontal className="mr-2 h-4 w-4" />
                  View Appraisal
                </Link>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="mobile-caption font-medium text-muted-foreground">Status:</span>
            <StatusBadge status={appraisal.status} />
          </div>
          <div className="flex items-center justify-between">
            <span className="mobile-caption font-medium text-muted-foreground">Last Updated:</span>
            <span className="mobile-body text-muted-foreground">
              {formatDateSafe(appraisal.lastUpdated)}
            </span>
          </div>
          {appraisal.role && (
            <div className="flex items-center justify-between">
              <span className="mobile-caption font-medium text-muted-foreground">Role:</span>
              <Badge variant={getRoleBadgeVariant(appraisal.role)}>
                {appraisal.role}
              </Badge>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )

  debug.log('🔍 [DEBUG] AppraisalDashboardTable - Received data:', data.map(item => ({
    fullName: item.fullName,
    departmentName: item.departmentName,
    status: item.status,
    role: item.role,
    isManager: item.isManager,
    managerName: item.managerName
  })))

  // Get unique values for filters
  const departments = React.useMemo(() => {
    const depts = Array.from(new Set(data.map(item => item.departmentName).filter(Boolean)))
    debug.log('🏢 [DEBUG] AppraisalDashboardTable - Unique departments:', depts)
    return depts.sort()
  }, [data])

  const roles = React.useMemo(() => {
    const roleList = Array.from(new Set(data.map(item => item.role).filter(Boolean)))
    debug.log('👔 [DEBUG] AppraisalDashboardTable - Unique roles:', roleList)
    return roleList.sort()
  }, [data])

  const statuses = React.useMemo(() => {
    const statusList = Array.from(new Set(data.map(item => item.status)))
    debug.log('📊 [DEBUG] AppraisalDashboardTable - Unique statuses:', statusList)
    return statusList.sort()
  }, [data])

  const columns: ColumnDef<EmployeeAppraisal>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "fullName",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-auto p-0 font-semibold"
        >
          <Users className="mr-2 h-4 w-4" />
          Employee
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => (
        <div className="flex flex-col">
          <Link 
            href={`/dashboard/employees/${row.original.employeeId}/profile`}
            className="font-medium text-blue-600 hover:text-blue-800 hover:underline"
          >
            {row.getValue("fullName")}
          </Link>
          {row.original.managerName && (
            <span className="text-xs text-muted-foreground">
              Reports to: {row.original.managerName}
            </span>
          )}
        </div>
      ),
    },
    {
      accessorKey: "departmentName",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-auto p-0 font-semibold"
        >
          <Building2 className="mr-2 h-4 w-4" />
          Department
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => (
        <span className="text-sm">{row.getValue("departmentName")}</span>
      ),
    },
    {
      accessorKey: "role",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-auto p-0 font-semibold"
        >
          Role
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const role = row.getValue("role") as UserRole
        return role ? (
          <Badge variant={getRoleBadgeVariant(role)} className="text-xs">
            {role.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
          </Badge>
        ) : (
          <span className="text-muted-foreground text-xs">N/A</span>
        )
      },
    },
    {
      accessorKey: "isManager",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-auto p-0 font-semibold"
        >
          <UserCheck className="mr-2 h-4 w-4" />
          Manager
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const isManager = row.getValue("isManager") as boolean
        return (
          <Badge variant={isManager ? "default" : "outline"} className="text-xs">
            {isManager ? "Yes" : "No"}
          </Badge>
        )
      },
    },
    {
      accessorKey: "status",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-auto p-0 font-semibold"
        >
          Status
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const status = row.getValue("status") as AppraisalStatus
        console.log('🏷️ [DEBUG] StatusBadge rendering for status:', status)
        return <StatusBadge status={status} />
      },
    },
    {
      accessorKey: "submittedAt",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-auto p-0 font-semibold"
        >
          <Calendar className="mr-2 h-4 w-4" />
          Submitted
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const submittedAt = row.getValue("submittedAt") as string | undefined
        const formattedDate = formatDateSafe(submittedAt)

        return (
          <span className={`text-sm ${formattedDate === '-' ? 'text-muted-foreground' : ''}`}>
            {formattedDate}
          </span>
        )
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <Button size="sm" asChild>
            <Link href={`/dashboard/appraisal/${row.original.employeeId}`}>
              {row.original.status === "not-started" ? "Start" : "View"}
            </Link>
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Quick Actions</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <Link href={`/dashboard/employees/${row.original.employeeId}/profile`} className="flex items-center">
                  <User className="mr-2 h-4 w-4" />
                  View Profile
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href={`/dashboard/appraisal/${row.original.employeeId}`} className="flex items-center">
                  <CheckCircle className="mr-2 h-4 w-4" />
                  {row.original.status === "not-started" ? "Start Appraisal" : "View Appraisal"}
                </Link>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      ),
    },
  ]

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onRowSelectionChange: setRowSelection,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    state: { sorting, columnFilters, rowSelection },
    enableRowSelection: true,
    getRowId: (row) => row.employeeId,
  })

  // Get selected rows
  const selectedRows = table.getFilteredSelectedRowModel().rows
  const selectedIds = selectedRows.map(row => row.original.employeeId)

  const handleBulkAction = (action: string) => {
    if (onBulkAction && selectedIds.length > 0) {
      onBulkAction(action, selectedIds)
      setRowSelection({})
    }
  }

  return (
    <div className="space-y-4">
      {/* Bulk Actions */}
      {selectedIds.length > 0 && (
        <div className="flex items-center gap-4 p-4 bg-muted rounded-lg">
          <div className="flex items-center gap-2">
            <CheckCircle className="h-4 w-4 text-primary" />
            <span className="text-sm font-medium">
              {selectedIds.length} item{selectedIds.length === 1 ? '' : 's'} selected
            </span>
          </div>
          <div className="flex items-center gap-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <MoreHorizontal className="h-4 w-4 mr-2" />
                  Bulk Actions
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuLabel>Actions for {selectedIds.length} items</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => handleBulkAction('approve')}>
                  Approve Selected
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleBulkAction('submit')}>
                  Submit Selected Drafts
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleBulkAction('ready-to-pay')}>
                  Mark Ready to Pay
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleBulkAction('contact-manager')}>
                  Mark Contact Manager
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => handleBulkAction('export')}>
                  Export Selected
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => setRowSelection({})}
            >
              Clear Selection
            </Button>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="flex flex-wrap gap-4 items-center">
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm font-medium">Filters:</span>
        </div>
        
        <Input
          placeholder="Search employees..."
          value={(table.getColumn("fullName")?.getFilterValue() as string) ?? ""}
          onChange={(event) => {
            table.getColumn("fullName")?.setFilterValue(event.target.value)
          }}
          className="max-w-sm"
        />

        <Select
          value={(table.getColumn("departmentName")?.getFilterValue() as string) ?? ""}
          onValueChange={(value) => {
            table.getColumn("departmentName")?.setFilterValue(value === "all" ? "" : value)
          }}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="All Departments" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Departments</SelectItem>
            {departments.map((dept) => (
              <SelectItem key={dept} value={dept}>
                {dept}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select
          value={(table.getColumn("status")?.getFilterValue() as string) ?? ""}
          onValueChange={(value) => {
            table.getColumn("status")?.setFilterValue(value === "all" ? "" : value)
          }}
        >
          <SelectTrigger className="w-[150px]">
            <SelectValue placeholder="All Statuses" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Statuses</SelectItem>
            {statuses.map((status) => (
              <SelectItem key={status} value={status}>
                {status.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select
          value={(table.getColumn("role")?.getFilterValue() as string) ?? ""}
          onValueChange={(value) => {
            table.getColumn("role")?.setFilterValue(value === "all" ? "" : value)
          }}
        >
          <SelectTrigger className="w-[150px]">
            <SelectValue placeholder="All Roles" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Roles</SelectItem>
            {roles.map((role) => (
              <SelectItem key={role} value={role ?? ""}>
                {role?.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select
          value={(table.getColumn("isManager")?.getFilterValue() as string) ?? ""}
          onValueChange={(value) => {
            table.getColumn("isManager")?.setFilterValue(
              value === "all" ? "" : value === "true"
            )
          }}
        >
          <SelectTrigger className="w-[150px]">
            <SelectValue placeholder="All Types" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Types</SelectItem>
            <SelectItem value="true">Managers Only</SelectItem>
            <SelectItem value="false">Non-Managers</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Mobile card view */}
      {isMobile ? (
        <div className="space-y-4">
          {table.getRowModel().rows?.length ? (
            table.getRowModel().rows.map((row) => (
              <MobileAppraisalCard key={row.id} appraisal={row.original} />
            ))
          ) : (
            <Card>
              <CardContent className="py-8">
                <p className="text-center text-muted-foreground">No employees found.</p>
              </CardContent>
            </Card>
          )}
        </div>
      ) : (
        /* Desktop table view */
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => (
                    <TableHead key={header.id}>
                      {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow key={row.id}>
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    No employees found.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Pagination */}
      <div className="flex items-center justify-between space-x-2 py-4">
        <div className="text-sm text-muted-foreground">
          Showing {table.getFilteredRowModel().rows.length} of {data.length} employees
        </div>
        <div className="space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  )
}
